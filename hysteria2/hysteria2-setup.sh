#!/usr/bin/env bash
#
# hysteria2-setup.sh - Hysteria2 一键配置脚本
# 用于快速配置 Hysteria2 服务器并生成客户端连接链接
# 支持 Shadowrocket、V2Ray、Sing-box 等客户端
#
# 使用方法: bash hysteria2-setup.sh
#

set -e

# 脚本配置
SCRIPT_NAME="$(basename "$0")"
CONFIG_DIR="/etc/hysteria"
CONFIG_FILE="$CONFIG_DIR/config.yaml"
EXECUTABLE_PATH="/usr/local/bin/hysteria"

# 颜色输出函数
tred() { echo -e "\033[31m$1\033[0m"; }
tgreen() { echo -e "\033[32m$1\033[0m"; }
tyellow() { echo -e "\033[33m$1\033[0m"; }
tblue() { echo -e "\033[34m$1\033[0m"; }
tbold() { echo -e "\033[1m$1\033[0m"; }

# 日志函数
info() { echo -e "$(tbold)[INFO]$(tblue) $1"; }
warn() { echo -e "$(tbold)[WARN]$(tyellow) $1"; }
error() { echo -e "$(tbold)[ERROR]$(tred) $1"; }
success() { echo -e "$(tbold)[SUCCESS]$(tgreen) $1"; }

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查Hysteria2是否已安装
check_hysteria_installed() {
    if [[ ! -f "$EXECUTABLE_PATH" ]]; then
        error "Hysteria2 未安装，请先运行安装脚本"
        info "安装命令: bash <(curl -fsSL https://get.hy2.sh/)"
        exit 1
    fi
}

# 获取服务器IP
get_server_ip() {
    local ip
    ip=$(curl -s4 ifconfig.me 2>/dev/null || curl -s4 icanhazip.com 2>/dev/null || curl -s4 ipinfo.io/ip 2>/dev/null)
    if [[ -z "$ip" ]]; then
        ip=$(ip route get ******* | awk '{print $7; exit}' 2>/dev/null)
    fi
    echo "$ip"
}

# 生成随机密码
generate_password() {
    openssl rand -base64 16 | tr -d "=+/" | cut -c1-16
}

# 生成随机端口
generate_port() {
    shuf -i 10000-65000 -n 1
}

# 获取用户输入
get_user_input() {
    local server_ip
    server_ip=$(get_server_ip)
    
    echo "$(tbold)=== Hysteria2 配置向导 ==="
    echo
    
    # 服务器地址
    read -p "请输入服务器地址 (默认: $server_ip): " SERVER_ADDR
    SERVER_ADDR=${SERVER_ADDR:-$server_ip}
    
    # 端口
    local default_port
    default_port=$(generate_port)
    read -p "请输入监听端口 (默认: $default_port): " SERVER_PORT
    SERVER_PORT=${SERVER_PORT:-$default_port}
    
    # 密码
    local default_password
    default_password=$(generate_password)
    read -p "请输入连接密码 (默认: $default_password): " AUTH_PASSWORD
    AUTH_PASSWORD=${AUTH_PASSWORD:-$default_password}
    
    # 域名 (可选)
    read -p "请输入域名 (可选，用于TLS证书): " DOMAIN_NAME
    
    # 伪装网站
    read -p "请输入伪装网站 (默认: https://www.bing.com): " MASQUERADE_URL
    MASQUERADE_URL=${MASQUERADE_URL:-"https://www.bing.com"}
    
    echo
    info "配置信息确认:"
    echo "服务器地址: $SERVER_ADDR"
    echo "监听端口: $SERVER_PORT"
    echo "连接密码: $AUTH_PASSWORD"
    echo "域名: ${DOMAIN_NAME:-"未设置"}"
    echo "伪装网站: $MASQUERADE_URL"
    echo
    
    read -p "确认配置? (y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        info "配置已取消"
        exit 0
    fi
}

# 生成配置文件
generate_config() {
    info "生成Hysteria2配置文件..."
    
    mkdir -p "$CONFIG_DIR"
    
    cat > "$CONFIG_FILE" << EOF
# Hysteria2 服务器配置文件
# 生成时间: $(date)

listen: :$SERVER_PORT

EOF

    # 如果设置了域名，使用ACME自动证书
    if [[ -n "$DOMAIN_NAME" ]]; then
        cat >> "$CONFIG_FILE" << EOF
acme:
  domains:
    - $DOMAIN_NAME
  email: admin@$DOMAIN_NAME

EOF
    else
        # 使用自签名证书
        cat >> "$CONFIG_FILE" << EOF
tls:
  cert: $CONFIG_DIR/server.crt
  key: $CONFIG_DIR/server.key

EOF
    fi
    
    cat >> "$CONFIG_FILE" << EOF
auth:
  type: password
  password: $AUTH_PASSWORD

masquerade:
  type: proxy
  proxy:
    url: $MASQUERADE_URL
    rewriteHost: true

bandwidth:
  up: 1 gbps
  down: 1 gbps

ignoreClientBandwidth: false
EOF

    success "配置文件已生成: $CONFIG_FILE"
}

# 生成自签名证书
generate_self_signed_cert() {
    if [[ -n "$DOMAIN_NAME" ]]; then
        return 0  # 使用ACME，不需要自签名证书
    fi
    
    info "生成自签名TLS证书..."
    
    openssl req -x509 -nodes -newkey rsa:2048 -keyout "$CONFIG_DIR/server.key" \
        -out "$CONFIG_DIR/server.crt" -days 365 -subj "/CN=$SERVER_ADDR" \
        >/dev/null 2>&1
    
    chmod 600 "$CONFIG_DIR/server.key"
    chmod 644 "$CONFIG_DIR/server.crt"
    
    success "自签名证书已生成"
}

# 配置防火墙
configure_firewall() {
    info "配置防火墙规则..."

    warn "即将开放所有端口，这可能存在安全风险"
    read -p "确认开放所有端口? (y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        info "跳过防火墙配置，请手动开放端口 $SERVER_PORT"
        return 0
    fi

    # UFW - 禁用防火墙
    if command -v ufw >/dev/null 2>&1; then
        info "禁用 UFW 防火墙..."
        ufw --force disable >/dev/null 2>&1 || true
        success "UFW 防火墙已禁用"
    fi

    # firewalld - 停止服务
    if command -v firewall-cmd >/dev/null 2>&1; then
        info "停止 firewalld 服务..."
        systemctl stop firewalld >/dev/null 2>&1 || true
        systemctl disable firewalld >/dev/null 2>&1 || true
        success "firewalld 服务已停止"
    fi

    # iptables - 清空规则并设置默认策略为ACCEPT
    if command -v iptables >/dev/null 2>&1; then
        info "清空 iptables 规则..."
        iptables -F >/dev/null 2>&1 || true
        iptables -X >/dev/null 2>&1 || true
        iptables -t nat -F >/dev/null 2>&1 || true
        iptables -t nat -X >/dev/null 2>&1 || true
        iptables -t mangle -F >/dev/null 2>&1 || true
        iptables -t mangle -X >/dev/null 2>&1 || true
        iptables -P INPUT ACCEPT >/dev/null 2>&1 || true
        iptables -P FORWARD ACCEPT >/dev/null 2>&1 || true
        iptables -P OUTPUT ACCEPT >/dev/null 2>&1 || true
        success "iptables 规则已清空，所有端口已开放"
    fi

    success "防火墙配置完成 - 所有端口已开放"
    warn "安全提醒: 建议后续根据需要配置具体的防火墙规则"
}

# 启动服务
start_service() {
    info "启动Hysteria2服务..."

    systemctl daemon-reload
    systemctl enable hysteria-server.service
    systemctl restart hysteria-server.service

    sleep 2

    if systemctl is-active --quiet hysteria-server.service; then
        success "Hysteria2服务启动成功"
    else
        error "Hysteria2服务启动失败"
        info "查看日志: journalctl -u hysteria-server.service -f"
        exit 1
    fi
}

# 生成客户端连接信息
generate_client_links() {
    info "生成客户端连接信息..."

    local server_name="Hysteria2-$(date +%m%d)"
    local insecure=""

    # 如果使用自签名证书，需要设置insecure
    if [[ -z "$DOMAIN_NAME" ]]; then
        insecure="&insecure=1"
    fi

    # Hysteria2 URI
    local hysteria2_uri="hysteria2://${AUTH_PASSWORD}@${SERVER_ADDR}:${SERVER_PORT}/?sni=${DOMAIN_NAME:-$SERVER_ADDR}${insecure}#${server_name}"

    # V2Ray/Xray JSON配置
    local v2ray_config=$(cat << EOF
{
  "tag": "$server_name",
  "protocol": "hysteria2",
  "settings": {
    "servers": [
      {
        "server": "$SERVER_ADDR",
        "port": $SERVER_PORT,
        "password": "$AUTH_PASSWORD"
      }
    ]
  },
  "streamSettings": {
    "network": "hysteria2",
    "security": "tls",
    "tlsSettings": {
      "serverName": "${DOMAIN_NAME:-$SERVER_ADDR}",
      "allowInsecure": $(if [[ -z "$DOMAIN_NAME" ]]; then echo "true"; else echo "false"; fi)
    }
  }
}
EOF
)

    # Sing-box配置
    local singbox_config=$(cat << EOF
{
  "tag": "$server_name",
  "type": "hysteria2",
  "server": "$SERVER_ADDR",
  "server_port": $SERVER_PORT,
  "password": "$AUTH_PASSWORD",
  "tls": {
    "enabled": true,
    "server_name": "${DOMAIN_NAME:-$SERVER_ADDR}",
    "insecure": $(if [[ -z "$DOMAIN_NAME" ]]; then echo "true"; else echo "false"; fi)
  }
}
EOF
)

    # Clash配置
    local clash_config=$(cat << EOF
- name: "$server_name"
  type: hysteria2
  server: $SERVER_ADDR
  port: $SERVER_PORT
  password: $AUTH_PASSWORD
  sni: ${DOMAIN_NAME:-$SERVER_ADDR}
  skip-cert-verify: $(if [[ -z "$DOMAIN_NAME" ]]; then echo "true"; else echo "false"; fi)
EOF
)

    # 保存配置到文件
    local output_dir="/root/hysteria2-client-configs"
    mkdir -p "$output_dir"

    echo "$hysteria2_uri" > "$output_dir/hysteria2-uri.txt"
    echo "$v2ray_config" > "$output_dir/v2ray-config.json"
    echo "$singbox_config" > "$output_dir/singbox-config.json"
    echo "$clash_config" > "$output_dir/clash-config.yaml"

    # 生成二维码 (如果安装了qrencode)
    if command -v qrencode >/dev/null 2>&1; then
        qrencode -t ANSIUTF8 "$hysteria2_uri" > "$output_dir/qrcode.txt"
    fi

    success "客户端配置已生成到: $output_dir"
}

# 显示连接信息
show_connection_info() {
    echo
    echo "$(tbold)========================================"
    echo "$(tgreen)    Hysteria2 配置完成!"
    echo "$(tbold)========================================"
    echo

    info "服务器信息:"
    echo "地址: $SERVER_ADDR"
    echo "端口: $SERVER_PORT"
    echo "密码: $AUTH_PASSWORD"
    echo "域名: ${DOMAIN_NAME:-"未设置 (使用自签名证书)"}"
    echo

    info "连接链接 (Shadowrocket/V2Ray等):"
    local insecure=""
    if [[ -z "$DOMAIN_NAME" ]]; then
        insecure="&insecure=1"
    fi
    local uri="hysteria2://${AUTH_PASSWORD}@${SERVER_ADDR}:${SERVER_PORT}/?sni=${DOMAIN_NAME:-$SERVER_ADDR}${insecure}#Hysteria2-$(date +%m%d)"
    echo "$uri"
    echo

    info "配置文件位置:"
    echo "服务器配置: $CONFIG_FILE"
    echo "客户端配置: /root/hysteria2-client-configs/"
    echo

    info "服务管理命令:"
    echo "启动服务: systemctl start hysteria-server"
    echo "停止服务: systemctl stop hysteria-server"
    echo "重启服务: systemctl restart hysteria-server"
    echo "查看状态: systemctl status hysteria-server"
    echo "查看日志: journalctl -u hysteria-server -f"
    echo

    if command -v qrencode >/dev/null 2>&1; then
        info "二维码:"
        qrencode -t ANSIUTF8 "$uri"
        echo
    else
        info "安装 qrencode 可显示二维码: apt install qrencode 或 yum install qrencode"
        echo
    fi

    warn "注意事项:"
    echo "1. 请确保服务器防火墙已开放端口 $SERVER_PORT"
    echo "2. 如果使用域名，请确保域名已正确解析到服务器IP"
    echo "3. 客户端配置文件已保存到 /root/hysteria2-client-configs/"
    if [[ -z "$DOMAIN_NAME" ]]; then
        echo "4. 使用自签名证书，客户端需要开启 '允许不安全连接' 或 'skip-cert-verify'"
    fi
    echo
}

# 显示现有配置
show_current_config() {
    if [[ ! -f "$CONFIG_FILE" ]]; then
        warn "未找到配置文件"
        return 1
    fi

    info "当前Hysteria2配置:"
    echo

    # 从配置文件读取信息
    local port=$(grep "listen:" "$CONFIG_FILE" | awk '{print $2}' | sed 's/://')
    local password=$(grep "password:" "$CONFIG_FILE" | awk '{print $2}')
    local domain=$(grep -A 2 "domains:" "$CONFIG_FILE" | grep "- " | awk '{print $2}' | head -1)
    local server_ip=$(get_server_ip)
    local masquerade=$(grep -A 3 "proxy:" "$CONFIG_FILE" | grep "url:" | awk '{print $2}')

    echo "服务器地址: $server_ip"
    echo "监听端口: $port"
    echo "连接密码: $password"
    echo "域名: ${domain:-"未设置 (使用自签名证书)"}"
    echo "伪装网站: ${masquerade:-"未设置"}"
    echo

    if systemctl is-active --quiet hysteria-server.service; then
        success "服务状态: 运行中"
    else
        error "服务状态: 已停止"
    fi
    echo

    # 生成客户端连接信息
    info "客户端连接信息:"
    echo

    local server_name="Hysteria2-$(date +%m%d)"
    local insecure=""

    # 如果使用自签名证书，需要设置insecure
    if [[ -z "$domain" ]]; then
        insecure="&insecure=1"
    fi

    # 通用连接链接
    local hysteria2_uri="hysteria2://${password}@${server_ip}:${port}/?sni=${domain:-$server_ip}${insecure}#${server_name}"

    info "1. 通用连接链接 (Shadowrocket/V2RayN/V2RayNG):"
    echo "$hysteria2_uri"
    echo

    # 显示二维码
    if command -v qrencode >/dev/null 2>&1; then
        info "2. 二维码:"
        qrencode -t ANSIUTF8 "$hysteria2_uri"
        echo
    else
        warn "未安装 qrencode，无法显示二维码"
        info "安装命令: apt install qrencode 或 yum install qrencode"
        echo
    fi

    # V2Ray/Xray 配置
    info "3. V2Ray/Xray JSON 配置:"
    cat << EOF
{
  "tag": "$server_name",
  "protocol": "hysteria2",
  "settings": {
    "servers": [
      {
        "server": "$server_ip",
        "port": $port,
        "password": "$password"
      }
    ]
  },
  "streamSettings": {
    "network": "hysteria2",
    "security": "tls",
    "tlsSettings": {
      "serverName": "${domain:-$server_ip}",
      "allowInsecure": $(if [[ -z "$domain" ]]; then echo "true"; else echo "false"; fi)
    }
  }
}
EOF
    echo

    # Sing-box 配置
    info "4. Sing-box 配置:"
    cat << EOF
{
  "tag": "$server_name",
  "type": "hysteria2",
  "server": "$server_ip",
  "server_port": $port,
  "password": "$password",
  "tls": {
    "enabled": true,
    "server_name": "${domain:-$server_ip}",
    "insecure": $(if [[ -z "$domain" ]]; then echo "true"; else echo "false"; fi)
  }
}
EOF
    echo

    # Clash 配置
    info "5. Clash 配置:"
    cat << EOF
- name: "$server_name"
  type: hysteria2
  server: $server_ip
  port: $port
  password: $password
  sni: ${domain:-$server_ip}
  skip-cert-verify: $(if [[ -z "$domain" ]]; then echo "true"; else echo "false"; fi)
EOF
    echo

    # 配置文件位置
    info "配置文件位置:"
    echo "服务器配置: $CONFIG_FILE"
    echo "客户端配置: /root/hysteria2-client-configs/"
    echo

    # 服务管理命令
    info "服务管理命令:"
    echo "启动服务: systemctl start hysteria-server"
    echo "停止服务: systemctl stop hysteria-server"
    echo "重启服务: systemctl restart hysteria-server"
    echo "查看状态: systemctl status hysteria-server"
    echo "查看日志: journalctl -u hysteria-server -f"
    echo

    # 注意事项
    warn "注意事项:"
    echo "1. 请确保服务器防火墙已开放端口 $port"
    echo "2. 如果使用域名，请确保域名已正确解析到服务器IP"
    if [[ -z "$domain" ]]; then
        echo "3. 使用自签名证书，客户端需要开启 '允许不安全连接' 或 'skip-cert-verify'"
    fi
    echo
}

# 重新生成连接信息
regenerate_links() {
    if [[ ! -f "$CONFIG_FILE" ]]; then
        error "未找到配置文件，请先运行完整配置"
        return 1
    fi

    # 从配置文件读取信息
    SERVER_PORT=$(grep "listen:" "$CONFIG_FILE" | awk '{print $2}' | sed 's/://')
    AUTH_PASSWORD=$(grep "password:" "$CONFIG_FILE" | awk '{print $2}')
    DOMAIN_NAME=$(grep -A 2 "domains:" "$CONFIG_FILE" | grep "- " | awk '{print $2}' | head -1)
    SERVER_ADDR=$(get_server_ip)

    generate_client_links
    show_connection_info
}

# 卸载Hysteria2
uninstall_hysteria2() {
    warn "这将完全卸载Hysteria2服务器"
    read -p "确认卸载? (y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        info "取消卸载"
        return 0
    fi

    info "停止服务..."
    systemctl stop hysteria-server.service >/dev/null 2>&1 || true
    systemctl disable hysteria-server.service >/dev/null 2>&1 || true

    info "删除文件..."
    rm -f "$EXECUTABLE_PATH"
    rm -rf "$CONFIG_DIR"
    rm -f /etc/systemd/system/hysteria-server.service
    rm -f /etc/systemd/system/hysteria-server@.service
    rm -rf /root/hysteria2-client-configs

    systemctl daemon-reload

    success "Hysteria2已完全卸载"
}

# 测试配置
test_configuration() {
    info "测试 Hysteria2 配置..."
    echo

    local test_passed=0
    local test_total=0

    # 测试1: 检查配置文件
    ((test_total++))
    if [[ -f "$CONFIG_FILE" ]]; then
        if "$EXECUTABLE_INSTALL_PATH" server --config "$CONFIG_FILE" --check >/dev/null 2>&1; then
            success "配置文件语法正确"
            ((test_passed++))
        else
            error "配置文件语法错误"
        fi
    else
        error "配置文件不存在"
    fi

    # 测试2: 检查服务状态
    ((test_total++))
    if systemctl is-active --quiet hysteria-server.service; then
        success "服务正在运行"
        ((test_passed++))
    else
        error "服务未运行"
    fi

    # 测试3: 检查端口监听
    ((test_total++))
    local port=$(grep "listen:" "$CONFIG_FILE" 2>/dev/null | awk '{print $2}' | sed 's/://' | head -1)
    if [[ -n "$port" ]] && (netstat -tuln 2>/dev/null | grep -q ":$port " || ss -tuln 2>/dev/null | grep -q ":$port "); then
        success "端口 $port 正在监听"
        ((test_passed++))
    else
        error "端口监听异常"
    fi

    # 测试4: 检查证书
    ((test_total++))
    if grep -q "acme:" "$CONFIG_FILE" 2>/dev/null; then
        success "使用 ACME 自动证书"
        ((test_passed++))
    else
        local cert_file=$(grep "cert:" "$CONFIG_FILE" 2>/dev/null | awk '{print $2}' | head -1)
        if [[ -f "$cert_file" ]]; then
            success "自签名证书存在"
            ((test_passed++))
        else
            error "证书文件不存在"
        fi
    fi

    echo
    if [[ $test_passed -eq $test_total ]]; then
        success "所有测试通过 ($test_passed/$test_total)"
        echo "$(tgreen)Hysteria2 服务器配置正确！"
    else
        warn "部分测试失败 ($test_passed/$test_total)"
        echo "$(tyellow)请检查上述问题"
    fi
    echo
}

# 显示菜单
show_menu() {
    clear
    echo "$(tbold)========================================"
    echo "$(tgreen)    Hysteria2 一键配置脚本"
    echo "$(tbold)========================================"
    echo
    echo "1. 完整配置 Hysteria2 服务器"
    echo "2. 显示当前配置信息"
    echo "3. 重新生成客户端连接信息"
    echo "4. 重启 Hysteria2 服务"
    echo "5. 查看服务状态"
    echo "6. 查看服务日志"
    echo "7. 测试配置"
    echo "8. 卸载 Hysteria2"
    echo "0. 退出"
    echo
}

# 主函数
main() {
    check_root

    if [[ $# -eq 0 ]]; then
        # 交互式菜单
        while true; do
            show_menu
            read -p "请选择操作 [0-8]: " choice
            echo

            case $choice in
                1)
                    check_hysteria_installed
                    get_user_input
                    generate_config
                    generate_self_signed_cert
                    configure_firewall
                    start_service
                    generate_client_links
                    show_connection_info
                    read -p "按回车键继续..."
                    ;;
                2)
                    show_current_config
                    read -p "按回车键继续..."
                    ;;
                3)
                    regenerate_links
                    read -p "按回车键继续..."
                    ;;
                4)
                    info "重启Hysteria2服务..."
                    systemctl restart hysteria-server.service
                    if systemctl is-active --quiet hysteria-server.service; then
                        success "服务重启成功"
                    else
                        error "服务重启失败"
                    fi
                    read -p "按回车键继续..."
                    ;;
                5)
                    info "Hysteria2服务状态:"
                    systemctl status hysteria-server.service --no-pager
                    read -p "按回车键继续..."
                    ;;
                6)
                    info "Hysteria2服务日志 (按Ctrl+C退出):"
                    journalctl -u hysteria-server.service -f
                    ;;
                7)
                    test_configuration
                    read -p "按回车键继续..."
                    ;;
                8)
                    uninstall_hysteria2
                    read -p "按回车键继续..."
                    ;;
                0)
                    info "退出脚本"
                    exit 0
                    ;;
                *)
                    error "无效选择，请重新输入"
                    read -p "按回车键继续..."
                    ;;
            esac
        done
    else
        # 命令行参数模式
        case "$1" in
            "install"|"setup")
                check_hysteria_installed
                get_user_input
                generate_config
                generate_self_signed_cert
                configure_firewall
                start_service
                generate_client_links
                show_connection_info
                ;;
            "status")
                show_current_config
                ;;
            "links")
                regenerate_links
                ;;
            "test")
                test_configuration
                ;;
            "uninstall")
                uninstall_hysteria2
                ;;
            *)
                echo "用法: $0 [install|status|links|test|uninstall]"
                echo "或直接运行 $0 进入交互式菜单"
                exit 1
                ;;
        esac
    fi
}

# 运行主函数
main "$@"
