# Hysteria2 一键配置脚本

这是一个用于快速配置 Hysteria2 代理服务器的脚本，可以生成适用于 Shadowrocket、V2Ray、Sing-box、Clash 等客户端的连接配置。

## 功能特点

- 🚀 一键安装和配置 Hysteria2 服务器
- 🔗 自动生成多种客户端连接配置
- 🛡️ 支持自签名证书和 ACME 自动证书
- 🎯 支持自定义伪装网站
- 📱 生成二维码方便移动端扫码
- 🔧 提供完整的服务管理功能

## 支持的客户端

- **Shadowrocket** (iOS)
- **V2Ray** / **V2RayN** / **V2RayNG**
- **Sing-box**
- **Clash** / **Clash for Windows**
- **Xray**

## 使用方法

### 1. 下载脚本

```bash
wget https://raw.githubusercontent.com/your-repo/hysteria2-setup.sh
# 或者
curl -O https://raw.githubusercontent.com/your-repo/hysteria2-setup.sh
```

### 2. 给脚本执行权限

```bash
chmod +x hysteria2-setup.sh
```

### 3. 运行脚本

#### 交互式菜单模式（推荐）

```bash
sudo ./hysteria2-setup.sh
```

#### 命令行模式

```bash
# 安装配置
sudo ./hysteria2-setup.sh install

# 查看状态
sudo ./hysteria2-setup.sh status

# 重新生成连接信息
sudo ./hysteria2-setup.sh links

# 测试配置
sudo ./hysteria2-setup.sh test

# 卸载
sudo ./hysteria2-setup.sh uninstall
```

## 前置要求

1. **系统要求**: Linux 系统 (Ubuntu/Debian/CentOS/RHEL)
2. **权限要求**: 需要 root 权限
3. **网络要求**: 服务器需要能够访问互联网
4. **Hysteria2**: 需要先安装 Hysteria2

### 安装 Hysteria2

如果还没有安装 Hysteria2，请先运行：

```bash
bash <(curl -fsSL https://get.hy2.sh/)
```

## 配置说明

### 服务器配置

脚本会引导你配置以下参数：

- **服务器地址**: 自动检测公网IP，也可手动输入
- **监听端口**: 随机生成或自定义端口
- **连接密码**: 随机生成或自定义密码
- **域名**: 可选，用于ACME自动证书
- **伪装网站**: 用于流量伪装，默认为 Bing

### 证书配置

- **有域名**: 使用 ACME 自动申请 Let's Encrypt 证书
- **无域名**: 使用自签名证书（客户端需要开启允许不安全连接）

## 生成的配置文件

脚本会在 `/root/hysteria2-client-configs/` 目录下生成以下文件：

- `hysteria2-uri.txt` - 通用连接链接
- `v2ray-config.json` - V2Ray/Xray 配置
- `singbox-config.json` - Sing-box 配置
- `clash-config.yaml` - Clash 配置
- `qrcode.txt` - 二维码（需要安装 qrencode）

## 客户端配置示例

脚本会自动生成各种客户端的配置文件，以下是主要客户端的使用方法：

### Shadowrocket (iOS)

**连接链接格式：**
```
hysteria2://your_password@server_ip:port/?sni=domain#Hysteria2-Server
```

**使用自签名证书（无域名）：**
```
hysteria2://your_password@server_ip:port/?sni=server_ip&insecure=1#Hysteria2-Server
```

**配置步骤：**
1. 打开 Shadowrocket
2. 点击右上角 "+"
3. 选择 "类型" → "Hysteria2"
4. 或直接扫描二维码/粘贴连接链接

### V2RayN/V2RayNG

**方法1：** 直接导入连接链接
**方法2：** 使用脚本生成的 JSON 配置文件

### Sing-box

将脚本生成的配置添加到 Sing-box 的 outbounds 中：
```json
{
  "tag": "hysteria2",
  "type": "hysteria2",
  "server": "server_ip",
  "server_port": port,
  "password": "your_password",
  "tls": {
    "enabled": true,
    "server_name": "domain",
    "insecure": false
  }
}
```

### Clash

将脚本生成的配置添加到 Clash 的 proxies 中：
```yaml
- name: "Hysteria2-Server"
  type: hysteria2
  server: server_ip
  port: port
  password: your_password
  sni: domain
  skip-cert-verify: false
```

## 服务管理

### 基本命令

```bash
# 启动服务
systemctl start hysteria-server

# 停止服务
systemctl stop hysteria-server

# 重启服务
systemctl restart hysteria-server

# 查看状态
systemctl status hysteria-server

# 查看日志
journalctl -u hysteria-server -f

# 开机自启
systemctl enable hysteria-server
```

### 配置文件位置

- 服务器配置: `/etc/hysteria/config.yaml`
- 客户端配置: `/root/hysteria2-client-configs/`
- 证书文件: `/etc/hysteria/` (自签名) 或 ACME 自动管理

## 防火墙配置

脚本会自动配置防火墙规则，支持：

- UFW
- firewalld  
- iptables

如果自动配置失败，请手动开放端口：

```bash
# UFW
ufw allow [端口号]

# firewalld
firewall-cmd --permanent --add-port=[端口号]/tcp
firewall-cmd --permanent --add-port=[端口号]/udp
firewall-cmd --reload

# iptables
iptables -I INPUT -p tcp --dport [端口号] -j ACCEPT
iptables -I INPUT -p udp --dport [端口号] -j ACCEPT
```

## 故障排除

### 1. 服务启动失败

```bash
# 查看详细日志
journalctl -u hysteria-server -f

# 检查配置文件
cat /etc/hysteria/config.yaml

# 测试配置
/usr/local/bin/hysteria server --config /etc/hysteria/config.yaml
```

### 2. 客户端连接失败

- 检查服务器防火墙是否开放端口
- 确认域名解析是否正确
- 如果使用自签名证书，确保客户端开启了"允许不安全连接"

### 3. 证书问题

```bash
# 检查证书文件
ls -la /etc/hysteria/

# 重新生成自签名证书
openssl req -x509 -nodes -newkey rsa:2048 -keyout /etc/hysteria/server.key \
    -out /etc/hysteria/server.crt -days 365 -subj "/CN=your-server-ip"
```

## 常见问题

### 1. 连接失败
- 检查服务器防火墙是否开放端口
- 确认域名解析是否正确
- 如果使用自签名证书，确保客户端开启了"允许不安全连接"

### 2. 服务启动失败
```bash
# 查看详细日志
journalctl -u hysteria-server -f

# 测试配置
./hysteria2-setup.sh test
```

### 3. 证书问题
- 有域名：确保域名正确解析到服务器IP
- 无域名：客户端需要开启 `insecure=1` 或 `skip-cert-verify=true`

## 注意事项

1. 请确保服务器时间准确，时间偏差可能导致连接失败
2. 如果使用域名，请确保域名已正确解析到服务器IP
3. 建议定期更新 Hysteria2 到最新版本
4. 生产环境建议使用域名和正式证书
5. 脚本会自动配置防火墙，如果失败请手动开放端口

## 功能特性

- ✅ 一键安装配置 Hysteria2
- ✅ 自动生成客户端配置
- ✅ 支持自签名和 ACME 证书
- ✅ 交互式菜单和命令行模式
- ✅ 内置配置测试功能
- ✅ 完整的服务管理
- ✅ 支持多种主流客户端

## 许可证

MIT License
