#!/bin/bash

# MongoDB 每小时备份脚本 - 修复版本
# 修复问题：
# 1. 使用北京时间
# 2. 保留所有备份文件，不自动清理
# 3. 确保每小时都能正常执行

# 配置变量
BACKUP_DIR="/root/mongodb_backups"
LOG_FILE="/var/log/mongodb_backup.log"

# 设置时区为北京时间
export TZ='Asia/Shanghai'

# 使用北京时间生成时间戳
DATE=$(TZ='Asia/Shanghai' date +%Y%m%d_%H%M%S)
BEIJING_TIME=$(TZ='Asia/Shanghai' date '+%Y-%m-%d %H:%M:%S')
DB_NAME="carmange-miniprogram"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 记录开始时间（使用北京时间）
echo "[$BEIJING_TIME] 开始备份 MongoDB 数据库 $DB_NAME" >> $LOG_FILE

# 检查MongoDB是否运行
if ! pgrep mongod > /dev/null; then
    echo "[$BEIJING_TIME] 错误: MongoDB 服务未运行!" >> $LOG_FILE
    exit 1
fi

# 执行备份
echo "[$BEIJING_TIME] 执行备份命令..." >> $LOG_FILE
mongodump --host 127.0.0.1:27017 --db $DB_NAME --username admin --password 123xierongfei --authenticationDatabase admin --out $BACKUP_DIR/backup_$DATE

# 检查备份是否成功
if [ $? -eq 0 ]; then
    # 压缩备份文件
    echo "[$BEIJING_TIME] 开始压缩备份文件..." >> $LOG_FILE
    tar -czf $BACKUP_DIR/backup_${DATE}.tar.gz -C $BACKUP_DIR backup_$DATE
    
    if [ $? -eq 0 ]; then
        # 删除未压缩的备份目录
        rm -rf $BACKUP_DIR/backup_$DATE
        
        # 获取压缩文件大小
        BACKUP_SIZE=$(du -sh $BACKUP_DIR/backup_${DATE}.tar.gz | cut -f1)
        
        echo "[$BEIJING_TIME] 备份成功完成 - 文件: backup_${DATE}.tar.gz, 大小: $BACKUP_SIZE" >> $LOG_FILE
        
        # 不再自动清理旧备份，保留所有备份文件
        TOTAL_BACKUPS=$(ls -1 $BACKUP_DIR/backup_*.tar.gz 2>/dev/null | wc -l)
        echo "[$BEIJING_TIME] 当前总备份文件数: $TOTAL_BACKUPS" >> $LOG_FILE
        
    else
        echo "[$BEIJING_TIME] 压缩失败！错误代码: $?" >> $LOG_FILE
        exit 1
    fi
else
    echo "[$BEIJING_TIME] 备份失败！错误代码: $?" >> $LOG_FILE
    exit 1
fi

# 验证备份文件
if [ -f "$BACKUP_DIR/backup_${DATE}.tar.gz" ]; then
    # 验证压缩文件完整性
    if tar -tzf "$BACKUP_DIR/backup_${DATE}.tar.gz" >/dev/null 2>&1; then
        echo "[$BEIJING_TIME] 备份文件验证成功: $BACKUP_DIR/backup_${DATE}.tar.gz" >> $LOG_FILE
    else
        echo "[$BEIJING_TIME] 错误: 备份文件损坏!" >> $LOG_FILE
        exit 1
    fi
else
    echo "[$BEIJING_TIME] 错误: 备份文件不存在!" >> $LOG_FILE
    exit 1
fi

# 显示磁盘使用情况
DISK_USAGE=$(df -h $BACKUP_DIR | tail -1 | awk '{print $5}')
echo "[$BEIJING_TIME] 备份目录磁盘使用率: $DISK_USAGE" >> $LOG_FILE

echo "[$BEIJING_TIME] 备份任务完成" >> $LOG_FILE
echo "----------------------------------------" >> $LOG_FILE

# 可选：发送备份成功通知（如果需要的话）
# echo "MongoDB备份完成: backup_${DATE}.tar.gz ($BACKUP_SIZE)" | mail -s "MongoDB备份成功" <EMAIL>
