#!/bin/bash
#
# VPS 初始化脚本
# 适用于 Debian/Ubuntu/CentOS 系统
# 使用方法: bash <(curl -fsSL https://your-domain.com/vps-init.sh)
#

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m'

info() { echo -e "${BLUE}[INFO]${NC} $1"; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检查root权限
if [[ $EUID -ne 0 ]]; then
    error "请使用root权限运行此脚本"
    exit 1
fi

echo -e "${BOLD}========================================"
echo -e "${GREEN}    VPS 初始化脚本"
echo -e "${BOLD}========================================"
echo ""

# 获取密码
echo "请设置root用户密码:"
while true; do
    echo -n "新密码: "
    read -s password1
    echo ""
    echo -n "确认密码: "
    read -s password2
    echo ""
    
    if [[ "$password1" == "$password2" ]] && [[ ${#password1} -ge 6 ]]; then
        break
    else
        warn "密码不匹配或长度不足6位，请重新输入"
    fi
done

info "开始初始化..."

# 检测系统类型
if [[ -f /etc/debian_version ]]; then
    OS_TYPE="debian"
    info "检测到 Debian/Ubuntu 系统"
elif [[ -f /etc/redhat-release ]]; then
    OS_TYPE="redhat"
    info "检测到 CentOS/RHEL 系统"
else
    OS_TYPE="unknown"
    warn "未知系统类型，使用通用配置"
fi

# 系统更新
info "更新系统..."
if [[ "$OS_TYPE" == "debian" ]]; then
    export DEBIAN_FRONTEND=noninteractive
    apt-get update -y >/dev/null 2>&1
    apt-get upgrade -y >/dev/null 2>&1
    apt-get autoremove -y >/dev/null 2>&1
elif [[ "$OS_TYPE" == "redhat" ]]; then
    if command -v dnf >/dev/null 2>&1; then
        dnf update -y >/dev/null 2>&1
    else
        yum update -y >/dev/null 2>&1
    fi
fi
success "系统更新完成"

# 安装基础软件
info "安装基础软件..."
if [[ "$OS_TYPE" == "debian" ]]; then
    apt-get install -y vim curl git unzip wget socat speedtest-cli vnstat htop net-tools >/dev/null 2>&1
elif [[ "$OS_TYPE" == "redhat" ]]; then
    if command -v dnf >/dev/null 2>&1; then
        dnf install -y epel-release >/dev/null 2>&1 || true
        dnf install -y vim curl git unzip wget socat htop net-tools >/dev/null 2>&1
    else
        yum install -y epel-release >/dev/null 2>&1 || true
        yum install -y vim curl git unzip wget socat htop net-tools >/dev/null 2>&1
    fi
fi
success "基础软件安装完成"

# 设置时区
info "设置时区..."
if command -v timedatectl >/dev/null 2>&1; then
    timedatectl set-timezone Asia/Shanghai >/dev/null 2>&1
else
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
fi
success "时区设置完成"

# 设置root密码
info "设置root密码..."
echo "root:$password1" | chpasswd
success "root密码设置完成"

# 备份SSH配置
SSH_CONFIG="/etc/ssh/sshd_config"
if [[ -f "$SSH_CONFIG" ]]; then
    cp "$SSH_CONFIG" "${SSH_CONFIG}.backup.$(date +%Y%m%d_%H%M%S)"
    info "SSH配置已备份"
fi

# 配置SSH - 兼容多种系统
info "配置SSH..."

# 方法1: 精确替换 (适用于大多数系统)
if [[ "$OS_TYPE" == "debian" ]]; then
    # Debian/Ubuntu 精确配置
    sed -i '/^#PermitRootLogin/s/^#//;s/prohibit-password/yes/' "$SSH_CONFIG" 2>/dev/null || true
    sed -i '/^#PasswordAuthentication/s/^#//;s/no/yes/' "$SSH_CONFIG" 2>/dev/null || true
    sed -i 's/^PermitRootLogin.*/PermitRootLogin yes/' "$SSH_CONFIG" 2>/dev/null || true
    sed -i 's/^PasswordAuthentication.*/PasswordAuthentication yes/' "$SSH_CONFIG" 2>/dev/null || true
elif [[ "$OS_TYPE" == "redhat" ]]; then
    # CentOS/RHEL 精确配置
    sed -i 's/PermitRootLogin no/PermitRootLogin yes/g' "$SSH_CONFIG" 2>/dev/null || true
    sed -i 's/PasswordAuthentication no/PasswordAuthentication yes/g' "$SSH_CONFIG" 2>/dev/null || true
    sed -i 's/^#PermitRootLogin.*/PermitRootLogin yes/' "$SSH_CONFIG" 2>/dev/null || true
    sed -i 's/^#PasswordAuthentication.*/PasswordAuthentication yes/' "$SSH_CONFIG" 2>/dev/null || true
fi

# 通用配置 - 确保设置生效
if ! grep -q "^PermitRootLogin yes" "$SSH_CONFIG"; then
    if grep -q "^PermitRootLogin" "$SSH_CONFIG"; then
        sed -i 's/^PermitRootLogin.*/PermitRootLogin yes/' "$SSH_CONFIG"
    elif grep -q "^#PermitRootLogin" "$SSH_CONFIG"; then
        sed -i 's/^#PermitRootLogin.*/PermitRootLogin yes/' "$SSH_CONFIG"
    else
        echo "PermitRootLogin yes" >> "$SSH_CONFIG"
    fi
fi

if ! grep -q "^PasswordAuthentication yes" "$SSH_CONFIG"; then
    if grep -q "^PasswordAuthentication" "$SSH_CONFIG"; then
        sed -i 's/^PasswordAuthentication.*/PasswordAuthentication yes/' "$SSH_CONFIG"
    elif grep -q "^#PasswordAuthentication" "$SSH_CONFIG"; then
        sed -i 's/^#PasswordAuthentication.*/PasswordAuthentication yes/' "$SSH_CONFIG"
    else
        echo "PasswordAuthentication yes" >> "$SSH_CONFIG"
    fi
fi

success "SSH配置完成"

# 重启SSH服务
info "重启SSH服务..."
if command -v systemctl >/dev/null 2>&1; then
    systemctl restart sshd >/dev/null 2>&1 || systemctl restart ssh >/dev/null 2>&1
elif command -v service >/dev/null 2>&1; then
    service sshd restart >/dev/null 2>&1 || service ssh restart >/dev/null 2>&1
else
    /etc/init.d/ssh restart >/dev/null 2>&1 || /etc/init.d/sshd restart >/dev/null 2>&1
fi
success "SSH服务重启完成"

# 获取服务器IP
SERVER_IP=$(curl -s4 ifconfig.me 2>/dev/null || curl -s4 icanhazip.com 2>/dev/null || echo "获取失败")

# 显示完成信息
echo ""
echo -e "${BOLD}========================================"
echo -e "${GREEN}    初始化完成!"
echo -e "${BOLD}========================================"
echo ""
echo -e "${GREEN}服务器信息:${NC}"
echo "IP地址: $SERVER_IP"
echo "用户名: root"
echo "密码: [您刚才设置的密码]"
echo ""
echo -e "${GREEN}SSH连接命令:${NC}"
echo "ssh root@$SERVER_IP"
echo ""
echo -e "${YELLOW}安全提醒:${NC}"
echo "1. 建议后续配置SSH密钥认证"
echo "2. 考虑更改SSH默认端口"
echo "3. 配置防火墙规则"
echo ""

# 显示当前时间验证时区设置
echo -e "${BLUE}当前服务器时间:${NC} $(date)"
echo ""
success "VPS初始化脚本执行完成!"
