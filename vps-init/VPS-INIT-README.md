# VPS 初始化脚本

这是一个用于新VPS服务器初始化的自动化脚本，支持 Debian、Ubuntu、CentOS 等主流Linux发行版。

## 📋 功能特点

- ✅ **系统更新**: 自动更新系统软件包
- ✅ **软件安装**: 安装常用基础软件
- ✅ **时区设置**: 设置为中国时区 (Asia/Shanghai)
- ✅ **SSH配置**: 启用root登录和密码认证
- ✅ **安全密码**: 交互式设置root密码
- ✅ **多系统支持**: 兼容 Debian/Ubuntu/CentOS
- ✅ **错误处理**: 完善的错误检查和日志输出

## 🚀 使用方法

### 方法1: 在线执行（推荐）

```bash
# 上传脚本到你的服务器后
bash <(curl -fsSL https://your-domain.com/vps-init.sh)
```

### 方法2: 下载后执行

```bash
# 下载脚本
wget https://your-domain.com/vps-init.sh
# 或者
curl -O https://your-domain.com/vps-init.sh

# 给予执行权限
chmod +x vps-init.sh

# 执行脚本
sudo ./vps-init.sh
```

## 📦 安装的软件包

### 通用软件
- `vim` - 文本编辑器
- `curl` - 网络请求工具
- `git` - 版本控制工具
- `unzip` - 解压工具
- `wget` - 下载工具
- `socat` - 网络工具
- `htop` - 系统监控
- `net-tools` - 网络工具

### Debian/Ubuntu 额外软件
- `speedtest-cli` - 网速测试
- `vnstat` - 网络流量统计

## 🔧 脚本执行流程

1. **权限检查** - 确保以root权限运行
2. **密码设置** - 交互式设置root密码
3. **系统检测** - 自动识别操作系统类型
4. **系统更新** - 更新软件包列表和已安装软件
5. **软件安装** - 安装基础软件包
6. **时区配置** - 设置为中国时区
7. **SSH配置** - 启用root登录和密码认证
8. **服务重启** - 重启SSH服务使配置生效

## 🛡️ 安全特性

### 密码安全
- 交互式密码输入（不在命令行显示）
- 密码确认机制
- 最小长度要求（6位）

### SSH配置安全
- 自动备份原SSH配置文件
- 兼容多种SSH配置格式
- 支持多种服务重启方式

### 系统兼容性
- 自动检测操作系统类型
- 针对不同系统使用相应的包管理器
- 优雅处理未知系统类型

## 📊 支持的操作系统

| 系统 | 版本 | 状态 |
|------|------|------|
| Ubuntu | 18.04+ | ✅ 完全支持 |
| Debian | 9+ | ✅ 完全支持 |
| CentOS | 7+ | ✅ 完全支持 |
| RHEL | 7+ | ✅ 完全支持 |
| AlmaLinux | 8+ | ✅ 完全支持 |
| Rocky Linux | 8+ | ✅ 完全支持 |
| Fedora | 30+ | ✅ 基本支持 |

## 🔍 原始代码问题分析

你提供的原始代码存在以下问题：

### 1. 安全风险
```bash
# ❌ 问题：密码硬编码在脚本中
echo "root:123xierongfei" | chpasswd

# ✅ 改进：交互式密码输入
echo -n "新密码: "
read -s password1
```

### 2. 兼容性问题
```bash
# ❌ 问题：sed表达式可能在某些系统上失效
sed -i 's/^#?PermitRootLogin./PermitRootLogin yes/g' /etc/ssh/sshd_config

# ✅ 改进：针对不同系统使用不同策略
if [[ "$OS_TYPE" == "debian" ]]; then
    sed -i '/^#PermitRootLogin/s/^#//;s/prohibit-password/yes/' "$SSH_CONFIG"
elif [[ "$OS_TYPE" == "redhat" ]]; then
    sed -i 's/PermitRootLogin no/PermitRootLogin yes/g' "$SSH_CONFIG"
fi
```

### 3. 错误处理缺失
```bash
# ❌ 问题：没有检查命令执行结果
systemctl restart sshd

# ✅ 改进：多重检查和错误处理
if command -v systemctl >/dev/null 2>&1; then
    systemctl restart sshd >/dev/null 2>&1 || systemctl restart ssh >/dev/null 2>&1
elif command -v service >/dev/null 2>&1; then
    service sshd restart >/dev/null 2>&1 || service ssh restart >/dev/null 2>&1
fi
```

## 🚨 注意事项

1. **备份重要数据**: 在生产服务器上运行前请备份重要配置
2. **网络连接**: 确保服务器有稳定的网络连接
3. **防火墙**: 脚本不会自动配置防火墙，请根据需要手动配置
4. **SSH密钥**: 建议后续配置SSH密钥认证以提高安全性

## 🔧 后续安全建议

脚本执行完成后，建议进行以下安全配置：

### 1. 配置SSH密钥认证
```bash
# 生成SSH密钥对（在本地执行）
ssh-keygen -t rsa -b 4096

# 上传公钥到服务器
ssh-copy-id root@your-server-ip
```

### 2. 更改SSH默认端口
```bash
# 编辑SSH配置
vim /etc/ssh/sshd_config

# 修改端口（例如改为2222）
Port 2222

# 重启SSH服务
systemctl restart sshd
```

### 3. 配置防火墙
```bash
# Ubuntu/Debian
ufw enable
ufw allow 2222/tcp  # 如果更改了SSH端口

# CentOS/RHEL
firewall-cmd --permanent --add-port=2222/tcp
firewall-cmd --reload
```

### 4. 禁用密码认证（配置密钥后）
```bash
# 编辑SSH配置
vim /etc/ssh/sshd_config

# 禁用密码认证
PasswordAuthentication no

# 重启SSH服务
systemctl restart sshd
```

## 📝 更新日志

- **v1.0**: 初始版本，支持基本的VPS初始化
- **v1.1**: 增加多系统兼容性
- **v1.2**: 改进SSH配置逻辑
- **v1.3**: 增加安全特性和错误处理

## 📞 技术支持

如果在使用过程中遇到问题，请检查：

1. 是否以root权限运行
2. 网络连接是否正常
3. 系统是否为支持的版本
4. SSH服务是否正常安装

## 📄 许可证

MIT License
